import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { SearchnewComponent } from '@app-appt/components';
import { CommonService } from 'his-components';
import { AppointmentsAPIService} from 'ecmed-api/appointments';
import { ApplicationService } from 'ec-ngcore';
import { SearchLayoutComponent } from 'ec-ngcore/ui';
import { SearchnewlistComponent } from '@app-appt/components';
import { FormGroup, FormControl, UntypedFormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { dateUtils } from '@app-appt/utils';
@Component({
  selector: 'app-searchlist',
  standalone: true,
  imports: [SearchLayoutComponent, CommonModule,  SearchnewComponent, SearchnewlistComponent,
            MatIconModule, MatButtonModule, MatDatepickerModule
  ],
  templateUrl: './searchlist.component.html',
  styleUrl: './searchlist.component.scss'
})
export class SearchlistComponent {
  filterValue: any;
  visitData?:any;
  range:UntypedFormGroup;
  applyFilter(data:any) {
    this.filterValue = data
  }
  showTable: boolean = false;
  appList: any;
  patient: object[]=[];
  appoinmentDetails: any;
  appointmentdata: any;

  constructor(
    private _HISAPI: AppointmentsAPIService,
    private router: Router,
    public commonServices: CommonService,
    public _appService: ApplicationService, private _currentRoute: ActivatedRoute,
  ) 
    { 
      let today = new Date();
          this.range = new FormGroup({
      start: new FormControl<Date|null>(today),
      end: new FormControl<Date|null>(dateUtils.addMonths(today,1))
    });
}

  ngOnInit(): void {
    this.commonServices.fetchVisitData(['ID']).subscribe(
      (response: any) => {
        this.visitData = response;
      }
    );

    let today = new Date();
    let endDay = new Date();
    endDay.setDate(today.getDate() + 30);

    let startDate = `${today.getFullYear()}/${today.getMonth() + 1}/${today.getDate()}`;
    let endDate = `${endDay.getFullYear()}/${endDay.getMonth() + 1}/${endDay.getDate()}`;
    this._HISAPI.appointmentsAPIGetAppointmentQueueGet({fromDate:startDate, toDate:endDate}).subscribe((data) => {
      this.appList = data;
    })
  }

  visitdate(data?:any) {
    if (!this.range?.value.start || !this.range.value.end) {
      this._appService.alertDialog({
        'title': 'Warning Error',
        message: 'DATE is Required.'
      });
      return;
    }
    if (!(this.range.controls["start"].hasError('matStartDateInvalid') || this.range.controls["end"].hasError('matEndDateInvalid'))) {
      let rangeStartDate = this.range?.value?.start
      let startDate = (rangeStartDate.toJSDate? rangeStartDate.toJSDate() :rangeStartDate).toISOString().split('T')[0];
      let rangeEndDate = this.range?.value?.end
      let endDate = (rangeEndDate.toJSDate? rangeEndDate.toJSDate() :rangeEndDate).toISOString().split('T')[0];
      this._HISAPI.appointmentsAPIGetAppointmentQueueGet(
               {fromDate:startDate?.toString(), toDate:endDate?.toString(),
                apptNo:data?.AptNo, idType:data?.idType, idNo:data?.idNo
               }).subscribe((data) => {
        
        if (data){
          this.appList = data;
          if (!data || this.appList.length == 0) {
            this.showTable = false;
            this._appService.alertDialog({
              'title': 'System Error',
              message: 'No Recorde Found.'
            });
          }
        }
   
      })
    }

  }

  public add() {
    this.router.navigate(['newapp'], { relativeTo: this._currentRoute })
  }
  onCheck(data:any) {
    // this.router.navigate(['his-psd/appointmentqueue/add', { IDENTIFIER: data.IDENTIFIER, INDIVIDUALID: data.INDIVIDUALID }])
  }
  //   
}
