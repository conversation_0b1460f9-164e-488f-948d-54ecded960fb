
import { CommonModule, DatePipe } from '@angular/common';
import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { PatientService } from 'ecmed-api/visitmgmt';
import { SearchLayoutComponent } from 'ec-ngcore/ui';
import { MatSelect } from '@angular/material/select';
import { AppointmentArrivalComponent } from '../appointment-arrival/appointment-arrival.component';

import { MatSnackBar } from '@angular/material/snack-bar';
import { ViewEncapsulation } from '@angular/core';
import { ApplicationService } from 'ec-ngcore';
import { FormsModule } from '@angular/forms';
import { MaterialPackage } from '@app-appt/utils';
import { SearchAppSchedulerComponent } from '@app-appt/components/search-appscheduler/search-appscheduler.component';
import { AppointmentSchedulerComponent } from '@app-appt/components/appointment-scheduler/appointment-scheduler.component';
import { MonthSchedulercomponent } from '@app-appt/components/month-scheduler/month-scheduler.component';
import { AppointmentsAPIFindResourcesGetRequestParams, AppointmentsAPIService } from 'ecmed-api/appointments';
import { lastValueFrom, Subject, Subscription, takeUntil } from 'rxjs';


@Component({
  standalone: true,
  imports: [SearchLayoutComponent, CommonModule, AppointmentArrivalComponent,
    SearchAppSchedulerComponent, FormsModule,
    AppointmentSchedulerComponent, MonthSchedulercomponent,
    MaterialPackage],
  templateUrl: './searchappointment.component.html',
  styleUrl: './searchappointment.component.scss',
  encapsulation: ViewEncapsulation.None,
  providers: [DatePipe]
})

export class SearchappointmentComponent {
  @ViewChild('select') select?: MatSelect;
  dateStatus: any = [];
  today = new Date();
  times: string[] = [];
  isOpen: boolean = false;
  fromTime: string = '';
  toTime: string = '';
  displayValue: string = '';
  objectsearch: any;
  searchPerformed: boolean = false;
  objloadlist: any;
  resourceBaseSearch: boolean = false;
  objsession: any = [];
  objfindresources: any;
  resourceSessions: any;
  objResources: any;
  headerdata: any;
  dynamicdata: any[] = [];
  weekDays: any = [];
  ResourceSessions = [];
  Sessions: [] = [];
  bookinglist = [];
  resourcesearch: any;
  filteredResources = [];
  searchTerm: string = '';
  initAll: any;
  showError: boolean = false
  objLoad: any = {}

  constructor(
    public _router: Router, public _datePipe: DatePipe,
    public _HISAPI: AppointmentsAPIService,
    public _API: PatientService, public _dialog: MatDialog, public snackbar: MatSnackBar,
    private _appService: ApplicationService
  ) {
    this.setDefaultDateRange();
    const today = new Date();
  }

  ngOnInit(): void {
    this._HISAPI.appointmentsAPIInitAllGet().subscribe(
      (response: any) => {
        this.initAll = response.Codes
      }
    );
  }

  get startDate(): Date {
    return new Date();
  }
  get endDate() {
    let dt = new Date();
    dt.setDate(dt.getDate() + 30);
    return dt;
  }

  setDefaultDateRange() {
    const today = new Date();
    const endDay = new Date();
    endDay.setDate(today.getDate() + 30);
    this.objLoad.fromDate = today;
    this.objLoad.toDate = endDay;
  }

  public objload(objLoad: any): void {
    let objload = {
      resourcetype: objLoad.resourcetype,
      fromDate: objLoad.fromDate,
      toDate: objLoad.toDate,
      clinic: objLoad.clinic,
      fromtime: objLoad.fromTime,
      totime: objLoad.toTime,
      speciality: objLoad.speciality,
      resource: objLoad.searchTerm,
    }
    this.objectsearch = objload;
  }

  public ResourceSearch(event: any): void {
    const resourceType = String(event);
    this._HISAPI.appointmentsAPIGetResourceDetailsForResourceTypeGet({ id: resourceType }).subscribe((result: any) => {
      if (result) {
        this.resourcesearch = result;
      }
    });
  }

  resetinputbox(): void {
    this.objLoad = {
      resourcetype: null,
      clinic: null,
      speciality: null,
      fromDate: null,
      toDate: null,
      searchTerm: null,
    };
    this.displayValue = '';
    this.searchTerm = '';
    this.searchPerformed = false;
    this.filteredResources = [];
    this.resourceBaseSearch = false;
    this.objsession = [];
    this.headerdata = [];
    this.objLoad.searchTerm = '';
  }

  public appointmentsFindResources(objLoad: any): void {
    let obj: AppointmentsAPIFindResourcesGetRequestParams = {
      resourceType: objLoad.resourcetype,
      fromDate: objLoad.fromDate.toJSON(),
      toDate: objLoad.toDate.toJSON(),
      clinic: objLoad.clinic,
      speciality: objLoad.speciality,
      fromTime: objLoad.fromTime,
      toTime: objLoad.toTime,
    }
    this.objloadlist = obj;
    this._HISAPI.appointmentsAPIFindResourcesGet(obj).subscribe((result: any) => {
      if (result) {
        this.dynamicdata = [];
        this.objfindresources = result;
        this.objsession = JSON.parse(result.SessionDetails)
        this.headerdata = [];
        this.dateStatus = result.DateStatus;
        for (var i = 0; i < this.dateStatus.length && i < 7; i++) {
          this.headerdata.push(
            {
              dateheader: this._datePipe.transform(result.DateStatus[i].ORGDATE, 'yyyy-MM-dd'),
              description: result.DateStatus[i].DESCRIPTION
            }
          )
        }
        for (var i = 0; i < this.objsession?.length; i++) {
          for (var j = 0; j < this.objsession[i].sessions?.length; j++) {

            let obj = this.objsession[i].sessions[j];
            obj.description = this.objsession[i].description;
            this.dynamicdata.push(obj);
          }
        }
        this.weekDays = result.DateStatus;
        this.objResources = result.Resources;
        this.bookinglist = result.ResourceSessions;
      }
    });
  }
  event = "Appointment"
  public onDiscard(data: any) {
    // if (data === "Appointment") {
    //   this.searchPerformed = false
    //   this.resourceBaseSearch = false
    // }
    this.event = data;
  }
  private destroy$ = new Subject<void>();
  public isApiCalling = false;
  private callCount = 0;
  private subscription: Subscription = new Subscription();

   public async appointmentsResourcesearch(objLoad: any): Promise<void> {
    try {
      this.isApiCalling = true;
      
      const observable = this._HISAPI.appointmentsAPIGetResourceSessionsGet({
        resourceId: objLoad.searchTerm?.toString(),
        fromDate: objLoad.fromDate.toJSON(),
        toDate: objLoad.toDate.toJSON(),
        clinic: objLoad.clinic,
        speciality: objLoad.speciality,
        fromTime: objLoad.fromTime,
        toTime: objLoad.toTime
      });
      
      const result = await lastValueFrom(observable);
      
      console.log(result, 'result get resource session');
      
    } catch (error) {
      console.error('API Error:', error);
    } finally {
      this.isApiCalling = false;
      this.callCount = 0;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.subscription.unsubscribe();
  }

  // public appointmentsResourcesearch(objLoad: any): void {
  //   const fromDate = this._datePipe.transform(objLoad.fromDate, 'yyyy-MM-dd') ?? '';
  //   const toDate = this._datePipe.transform(objLoad.toDate, 'yyyy-MM-dd') ?? '';
  //   this._HISAPI.appointmentsAPIGetResourceSessionsGet({
  //     resourceId: objLoad.searchTerm?.toString(),
  //     fromDate: fromDate,
  //     toDate: toDate,
  //     clinic: objLoad.clinic,
  //     speciality: objLoad.speciality,
  //     fromTime: objLoad.fromTime,
  //     toTime: objLoad.toTime
  //   }).subscribe({
  //     next: ((result: any) => {
  //       console.log(result, 'result get resource session');
  //       if (result) {
  //         this.dynamicdata = [];
  //         this.objfindresources = result;
  //         this.objsession = JSON.parse(result.SessionDetails)
  //         this.headerdata = [];
  //         this.dateStatus = result.DateStatus;
  //         for (var i = 0; i < this.dateStatus?.length; i++) {
  //           this.headerdata.push(
  //             {
  //               dateheader: this._datePipe.transform(result.DateStatus[i].ORGDATE, 'yyyy-MM-dd'),
  //               description: result.DateStatus[i].DESCRIPTION
  //             }
  //           )
  //         }
  //         for (var i = 0; i < this.objsession?.length; i++) {
  //           for (var j = 0; j < this.objsession[i].sessions?.length; j++) {

  //             let obj = this.objsession[i].sessions[j];
  //             obj.description = this.objsession[i].description;
  //             this.dynamicdata.push(obj);
  //           }
  //         }
  //         this.weekDays = result.DateStatus;
  //         console.log(this.headerdata, 'weekdaysnow');
  //         this.objResources = result.Resources;
  //         this.bookinglist = result.ResourceSessions;
  //         if (!this.bookinglist || this.bookinglist.length === 0) {
  //           this._appService.alertDialog({
  //             'title': 'System Error',
  //             message: 'Data not found'
  //           });
  //         }

  //       }
  //     }),
  //     error: ((error) => {
  //       // This block will only execute if catchError is used
  //       console.log('Error handler:', error);
  //     }),
  //     complete: () => { console.log('Completed') }
  //   });
  // }

  performSearch(objLoad: any): void {
    if (!objLoad.resourcetype) {
      this._appService.alertDialog({
        'title': 'System Error',
        message: 'Resource Type is Required.'
      });

      this.showError = true
      return
    }
    else if (!objLoad.fromDate && !objLoad.toDate) {
      this._appService.alertDialog({
        'title': 'System Error',
        message: 'From Date and To Date is Required.'
      });

      this.showError = true
      return
    }
    else if (!objLoad.fromDate) {
      this._appService.alertDialog({
        'title': 'System Error',
        message: 'From Date is Required.'
      });

      this.showError = true
      return

    }
    else if (!objLoad.toDate) {
      this._appService.alertDialog({
        'title': 'System Error',
        message: 'To Date is Required.'
      });

      this.showError = true
      return
    }
    else if (objLoad.searchTerm) {
      this.resourceBaseSearch = true;
      this.appointmentsResourcesearch(objLoad);
    } else {
      this.resourceBaseSearch = false;
      this.appointmentsFindResources(objLoad);
    }
    this.searchPerformed = true;
    this.objload(objLoad);
  }

  public openArrival(data: any) {
    // if (data === "Appointment") {
    //   this.searchPerformed = false
    //   this.resourceBaseSearch = false
    // }
    this.event = data;
  }

  backapp() {
    this._router.navigate(['appointments'])
  }

}